// LiveKit + Deepgram 实时转录代理

import {
  RoomServiceClient,
  AccessToken,
  Room,
  RoomEvent,
  TrackKind,
} from "livekit-server-sdk";
import { createClient } from "@deepgram/sdk";
import WebSocket from "ws";
import { config } from "dotenv";

// LiveKit + Deepgram 实时转录代理
// 使用真实的 LiveKit RTC 连接接收音频流并发送到 Deepgram

// 加载环境变量
config();

console.log("🔧 环境变量检查:");
console.log("LIVEKIT_URL:", process.env.LIVEKIT_URL);
console.log(
  "LIVEKIT_API_KEY:",
  process.env.LIVEKIT_API_KEY ? "已设置" : "未设置"
);
console.log(
  "LIVEKIT_API_SECRET:",
  process.env.LIVEKIT_API_SECRET ? "已设置" : "未设置"
);
console.log(
  "DEEPGRAM_API_KEY:",
  process.env.DEEPGRAM_API_KEY ? "已设置" : "未设置"
);

// 转录管理器
class TranscriptionManager {
  constructor(roomService, roomName) {
    this.roomService = roomService;
    this.roomName = roomName;
    this.deepgram = createClient(process.env.DEEPGRAM_API_KEY);
    this.activeConnections = new Map();
    this.transcriptionResults = [];
    this.rtcRoom = null; // LiveKit RTC 房间连接
    this.keepAliveIntervals = new Map(); // 保活定时器
  }

  // 连接到 LiveKit 房间作为转录代理
  async connectToRoom() {
    try {
      console.log("🔗 转录代理连接到 LiveKit 房间...");

      // 创建访问令牌
      const token = new AccessToken(
        process.env.LIVEKIT_API_KEY,
        process.env.LIVEKIT_API_SECRET,
        {
          identity: "transcription-agent",
          ttl: "24h", // 24小时有效期
        }
      );

      token.addGrant({
        room: this.roomName,
        roomJoin: true,
        canPublish: true,
        canSubscribe: true,
        canPublishData: true,
      });

      const jwt = await token.toJwt();

      // 创建 RTC 房间连接
      this.rtcRoom = new Room();

      // 监听房间事件
      this.rtcRoom.on(RoomEvent.Connected, () => {
        console.log("✅ 转录代理已连接到房间");
      });

      this.rtcRoom.on(RoomEvent.Disconnected, () => {
        console.log("🔌 转录代理已断开房间连接");
      });

      this.rtcRoom.on(
        RoomEvent.TrackSubscribed,
        (track, publication, participant) => {
          if (track.kind === TrackKind.Audio) {
            console.log(`🎵 订阅到音频轨道: ${participant.identity}`);
            this.handleAudioTrack(track, participant.identity);
          }
        }
      );

      this.rtcRoom.on(
        RoomEvent.TrackUnsubscribed,
        (track, publication, participant) => {
          if (track.kind === TrackKind.Audio) {
            console.log(`🔇 取消订阅音频轨道: ${participant.identity}`);
            this.stopTranscription(participant.identity);
          }
        }
      );

      // 连接到房间
      await this.rtcRoom.connect(process.env.LIVEKIT_URL, jwt);
      console.log("✅ 转录代理已成功连接到 LiveKit 房间");
    } catch (error) {
      console.error("❌ 转录代理连接房间失败:", error);
      throw error;
    }
  }

  // 处理音频轨道
  async handleAudioTrack(audioTrack, participantId) {
    console.log(`🎤 开始处理参与者 ${participantId} 的音频轨道`);

    // 启动该参与者的转录
    await this.startTranscription(participantId, audioTrack);
  }

  async startTranscription(participantId, audioTrack = null) {
    if (this.activeConnections.has(participantId)) {
      console.log(`🔄 参与者 ${participantId} 的转录已在进行中`);
      return;
    }

    console.log(`🎙️ 开始为参与者 ${participantId} 启动转录...`);

    try {
      // 创建 Deepgram 实时转录连接 - 优化配置减少丢单词
      const connection = this.deepgram.listen.live({
        model: "nova-2",
        language: "en", // 改为英语，匹配您的测试内容
        smart_format: true,
        punctuate: true,
        interim_results: true,
        endpointing: false, // 禁用自动端点检测，避免过早截断
        vad_events: true, // 启用语音活动检测事件
        utterances: true,
        utt_split: 0.8, // 增加分割阈值，减少过度分割
        numerals: true, // 正确格式化数字
        profanity_filter: false,
        filler_words: false,
        multichannel: false,
        alternatives: 1, // 只返回最佳结果
        sample_rate: 16000, // 设置采样率
        encoding: "linear16", // 设置编码格式
      });

      // 监听转录结果
      connection.on("Results", async (data) => {
        // console.log(`🔍 收到 Deepgram 结果 [${participantId}]:`, JSON.stringify(data, null, 2));

        const result = data.channel.alternatives[0];
        if (result && result.transcript && result.transcript.trim()) {
          const transcription = {
            type: data.is_final ? "transcription" : "interim_transcription",
            text: result.transcript.trim(),
            confidence: result.confidence || 0,
            participant: participantId,
            timestamp: Date.now(),
            isFinal: data.is_final,
            speechFinal: data.speech_final || false,
          };

          console.log(
            `📝 ${
              data.is_final ? "最终" : "临时"
            }转录 [${participantId}]: ${result.transcript.trim()} (置信度: ${(
              result.confidence * 100
            ).toFixed(1)}%)`
          );

          // 保存转录结果
          if (data.is_final) {
            this.transcriptionResults.push(transcription);
          }

          // 广播转录结果
          await this.broadcastTranscription(transcription);
        } else {
          // console.log(`🔍 Deepgram 结果无转录文本 [${participantId}]:`, data);
        }
      });

      // 监听语音活动检测事件
      connection.on("SpeechStarted", () => {
        console.log(`🎤 检测到语音开始 [${participantId}]`);
      });

      connection.on("UtteranceEnd", () => {
        console.log(`🔚 语音段落结束 [${participantId}]`);
      });

      connection.on("Error", (error) => {
        console.error(`❌ Deepgram 错误 [${participantId}]:`, error);
      });

      connection.on("Close", () => {
        console.log(`🔌 Deepgram 连接已关闭 [${participantId}]`);
        this.activeConnections.delete(participantId);
        this.stopKeepAlive(participantId);
      });

      connection.on("Open", () => {
        console.log(`🔗 Deepgram 连接已建立 [${participantId}]`);
        // 启动保活机制
        this.startKeepAlive(connection, participantId);
      });

      connection.on("Metadata", (metadata) => {
        console.log(`📊 Deepgram 元数据 [${participantId}]:`, metadata);
      });

      // 处理真实音频流
      if (audioTrack) {
        this.processAudioTrack(connection, audioTrack, participantId);
      } else {
        // 如果没有音频轨道，使用模拟音频流（向后兼容）
        this.simulateAudioStream(connection, participantId);
      }

      this.activeConnections.set(participantId, connection);
      console.log(`✅ 转录已启动 [${participantId}]`);
    } catch (error) {
      console.error(`❌ 启动转录失败 [${participantId}]:`, error);
    }
  }

  // 处理真实的音频轨道
  processAudioTrack(connection, audioTrack, participantId) {
    console.log(`🎵 开始处理真实音频流 [${participantId}]`);

    try {
      // 监听音频数据
      audioTrack.on("data", (audioData) => {
        if (connection.getReadyState() === 1) {
          // WebSocket.OPEN
          // 发送音频数据到 Deepgram
          connection.send(audioData);

          // 定期记录音频数据统计
          if (!this.audioStats) {
            this.audioStats = { totalChunks: 0, totalBytes: 0 };
          }
          this.audioStats.totalChunks++;
          this.audioStats.totalBytes += audioData.length;

          if (this.audioStats.totalChunks % 100 === 0) {
            console.log(
              `📊 音频统计 [${participantId}]: 已发送 ${this.audioStats.totalChunks} 块，总计 ${this.audioStats.totalBytes} 字节`
            );
          }
        }
      });

      audioTrack.on("ended", () => {
        console.log(`🔇 音频轨道结束 [${participantId}]`);
        this.stopTranscription(participantId);
      });

      console.log(`✅ 音频轨道处理已启动 [${participantId}]`);
    } catch (error) {
      console.error(`❌ 处理音频轨道失败 [${participantId}]:`, error);
    }
  }

  // 启动保活机制
  startKeepAlive(connection, participantId) {
    const interval = setInterval(() => {
      if (connection.getReadyState() === 1) {
        // WebSocket.OPEN
        connection.send(JSON.stringify({ type: "KeepAlive" }));
        console.log(`💓 发送保活消息 [${participantId}]`);
      } else {
        this.stopKeepAlive(participantId);
      }
    }, 8000); // 每8秒发送一次

    this.keepAliveIntervals.set(participantId, interval);
  }

  // 停止保活机制
  stopKeepAlive(participantId) {
    const interval = this.keepAliveIntervals.get(participantId);
    if (interval) {
      clearInterval(interval);
      this.keepAliveIntervals.delete(participantId);
      console.log(`💔 保活机制已停止 [${participantId}]`);
    }
  }

  simulateAudioStream(connection, participantId) {
    // 模拟音频流处理 - 发送测试音频以验证 Deepgram 连接
    console.log(`🎵 开始音频流处理 [${participantId}]`);

    // 等待连接建立
    setTimeout(() => {
      console.log(`🔊 发送测试音频数据到 Deepgram [${participantId}]`);

      try {
        // 发送一些静音数据来测试连接
        const silentAudio = new ArrayBuffer(1024);
        connection.send(silentAudio);
        console.log(`✅ 成功发送静音数据 [${participantId}]`);

        // 模拟说话检测
        setTimeout(() => {
          console.log(`🎤 模拟检测到语音活动 [${participantId}]`);

          // 发送一些模拟的音频数据
          try {
            const testAudio = new ArrayBuffer(2048);
            connection.send(testAudio);
            console.log(`✅ 成功发送测试音频 [${participantId}]`);
          } catch (error) {
            console.error(`❌ 发送测试音频失败 [${participantId}]:`, error);
          }
        }, 2000);
      } catch (error) {
        console.error(`❌ 发送静音数据失败 [${participantId}]:`, error);
      }
    }, 1000);
  }

  async stopTranscription(participantId) {
    const connection = this.activeConnections.get(participantId);
    if (connection) {
      connection.finish();
      this.activeConnections.delete(participantId);
      console.log(`🛑 已停止转录 [${participantId}]`);
    }
  }

  async broadcastTranscription(transcription) {
    try {
      console.log("📤 广播转录结果:", {
        participant: transcription.participant,
        text: transcription.text,
        isFinal: transcription.isFinal,
        confidence: transcription.confidence,
      });

      // 在实际应用中，这里应该通过 LiveKit 的数据通道发送到前端
      // 或者通过 WebSocket 直接发送到前端应用
    } catch (error) {
      console.error("❌ 广播转录结果失败:", error);
    }
  }

  getTranscriptionHistory() {
    return this.transcriptionResults;
  }

  getStats() {
    const totalTranscriptions = this.transcriptionResults.length;
    const avgConfidence =
      totalTranscriptions > 0
        ? this.transcriptionResults.reduce((sum, t) => sum + t.confidence, 0) /
          totalTranscriptions
        : 0;

    return {
      totalTranscriptions,
      avgConfidence: (avgConfidence * 100).toFixed(1),
      activeConnections: this.activeConnections.size,
    };
  }
}

async function startTranscriptionAgent() {
  try {
    console.log("🚀 启动实时转录代理...");

    // 创建 RoomService 客户端
    const roomService = new RoomServiceClient(
      process.env.LIVEKIT_URL,
      process.env.LIVEKIT_API_KEY,
      process.env.LIVEKIT_API_SECRET
    );

    // 测试连接
    console.log("📡 测试 LiveKit 服务器连接...");
    const rooms = await roomService.listRooms();
    console.log("✅ 成功连接到 LiveKit 服务器");
    console.log(`📊 当前房间数量: ${rooms.length}`);

    // 创建转录房间
    const roomName = "transcription-room";
    console.log(`🏠 确保房间 "${roomName}" 存在...`);

    try {
      await roomService.createRoom({
        name: roomName,
        emptyTimeout: 300,
        maxParticipants: 10,
      });
      console.log(`✅ 房间 "${roomName}" 已创建或已存在`);
    } catch (error) {
      if (error.message.includes("already exists")) {
        console.log(`✅ 房间 "${roomName}" 已存在`);
      } else {
        throw error;
      }
    }

    // 初始化转录管理器
    const transcriptionManager = new TranscriptionManager(
      roomService,
      roomName
    );
    console.log("🎙️ 转录管理器已初始化");

    // 监听房间参与者变化
    let previousParticipants = new Set();

    const checkParticipants = async () => {
      try {
        const participants = await roomService.listParticipants(roomName);
        const currentParticipants = new Set(
          participants.map((p) => p.identity)
        );

        // 检查新加入的参与者
        for (const participantId of currentParticipants) {
          if (
            !previousParticipants.has(participantId) &&
            participantId !== "transcription-agent"
          ) {
            console.log(`👤 新参与者加入: ${participantId}`);
            await transcriptionManager.startTranscription(participantId);
          }
        }

        // 检查离开的参与者
        for (const participantId of previousParticipants) {
          if (!currentParticipants.has(participantId)) {
            console.log(`👋 参与者离开: ${participantId}`);
            await transcriptionManager.stopTranscription(participantId);
          }
        }

        previousParticipants = currentParticipants;

        if (participants.length > 0) {
          const stats = transcriptionManager.getStats();
          console.log(
            `💓 房间状态: ${participants.length} 个参与者 | 转录统计: ${stats.totalTranscriptions} 条记录, 平均置信度: ${stats.avgConfidence}%`
          );
        } else {
          console.log("📊 房间中暂无参与者");
        }
      } catch (error) {
        if (error.message.includes("does not exist")) {
          console.log("📊 等待房间创建...");
        } else {
          console.log("📊 房间状态检查错误:", error.message);
        }
      }
    };

    // 定期检查参与者状态
    setInterval(checkParticipants, 5000);

    console.log("🚀 实时转录代理已启动并准备就绪！");
    console.log(
      "📱 您现在可以在浏览器中访问 http://localhost:5000 来测试转录功能"
    );
    console.log("🎤 当参与者加入房间并开始说话时，将自动开始转录");
    console.log("📝 转录结果将显示在控制台中");
  } catch (error) {
    console.error("❌ 转录代理启动失败:", error);
    console.error("错误详情:", error.stack);
  }
}

// 启动转录代理
startTranscriptionAgent();
