// LiveKit + Deepgram 实时转录代理

import { RoomServiceClient, AccessToken } from "livekit-server-sdk";
import { createClient } from "@deepgram/sdk";
import WebSocket from "ws";
import { config } from "dotenv";

// 注意：这个转录代理目前使用模拟音频流
// 要接收真实音频，需要使用 LiveKit 的 RTC 客户端连接

// 加载环境变量
config();

console.log("🔧 环境变量检查:");
console.log("LIVEKIT_URL:", process.env.LIVEKIT_URL);
console.log(
  "LIVEKIT_API_KEY:",
  process.env.LIVEKIT_API_KEY ? "已设置" : "未设置"
);
console.log(
  "LIVEKIT_API_SECRET:",
  process.env.LIVEKIT_API_SECRET ? "已设置" : "未设置"
);
console.log(
  "DEEPGRAM_API_KEY:",
  process.env.DEEPGRAM_API_KEY ? "已设置" : "未设置"
);

// 转录管理器
class TranscriptionManager {
  constructor(roomService, roomName) {
    this.roomService = roomService;
    this.roomName = roomName;
    this.deepgram = createClient(process.env.DEEPGRAM_API_KEY);
    this.activeConnections = new Map();
    this.transcriptionResults = [];
  }

  async startTranscription(participantId) {
    if (this.activeConnections.has(participantId)) {
      console.log(`🔄 参与者 ${participantId} 的转录已在进行中`);
      return;
    }

    console.log(`🎙️ 开始为参与者 ${participantId} 启动转录...`);

    try {
      // 创建 Deepgram 实时转录连接
      const connection = this.deepgram.listen.live({
        model: "nova-2",
        language: "zh-CN",
        smart_format: true,
        punctuate: true,
        interim_results: true,
        endpointing: 300, // 300ms 的静音检测
      });

      // 监听转录结果
      connection.on("Results", async (data) => {
        console.log(
          `🔍 收到 Deepgram 结果 [${participantId}]:`,
          JSON.stringify(data, null, 2)
        );

        const result = data.channel.alternatives[0];
        if (result && result.transcript) {
          const transcription = {
            type: result.is_final ? "transcription" : "interim_transcription",
            text: result.transcript,
            confidence: result.confidence || 0,
            participant: participantId,
            timestamp: Date.now(),
            isFinal: result.is_final,
          };

          console.log(
            `📝 ${result.is_final ? "最终" : "临时"}转录 [${participantId}]: ${
              result.transcript
            } (置信度: ${(result.confidence * 100).toFixed(1)}%)`
          );

          // 保存转录结果
          if (result.is_final) {
            this.transcriptionResults.push(transcription);
          }

          // 广播转录结果
          await this.broadcastTranscription(transcription);
        } else {
          console.log(`🔍 Deepgram 结果无转录文本 [${participantId}]:`, data);
        }
      });

      connection.on("Error", (error) => {
        console.error(`❌ Deepgram 错误 [${participantId}]:`, error);
      });

      connection.on("Close", () => {
        console.log(`🔌 Deepgram 连接已关闭 [${participantId}]`);
        this.activeConnections.delete(participantId);
      });

      connection.on("Open", () => {
        console.log(`🔗 Deepgram 连接已建立 [${participantId}]`);
      });

      connection.on("Metadata", (metadata) => {
        console.log(`📊 Deepgram 元数据 [${participantId}]:`, metadata);
      });

      // 模拟音频数据流 - 在实际应用中，这里应该连接到 LiveKit 的音频流
      this.simulateAudioStream(connection, participantId);

      this.activeConnections.set(participantId, connection);
      console.log(`✅ 转录已启动 [${participantId}]`);
    } catch (error) {
      console.error(`❌ 启动转录失败 [${participantId}]:`, error);
    }
  }

  simulateAudioStream(connection, participantId) {
    // 模拟音频流处理 - 发送测试音频以验证 Deepgram 连接
    console.log(`🎵 开始音频流处理 [${participantId}]`);

    // 等待连接建立
    setTimeout(() => {
      console.log(`🔊 发送测试音频数据到 Deepgram [${participantId}]`);

      try {
        // 发送一些静音数据来测试连接
        const silentAudio = new ArrayBuffer(1024);
        connection.send(silentAudio);
        console.log(`✅ 成功发送静音数据 [${participantId}]`);

        // 模拟说话检测
        setTimeout(() => {
          console.log(`🎤 模拟检测到语音活动 [${participantId}]`);

          // 发送一些模拟的音频数据
          try {
            const testAudio = new ArrayBuffer(2048);
            connection.send(testAudio);
            console.log(`✅ 成功发送测试音频 [${participantId}]`);
          } catch (error) {
            console.error(`❌ 发送测试音频失败 [${participantId}]:`, error);
          }
        }, 2000);
      } catch (error) {
        console.error(`❌ 发送静音数据失败 [${participantId}]:`, error);
      }
    }, 1000);
  }

  async stopTranscription(participantId) {
    const connection = this.activeConnections.get(participantId);
    if (connection) {
      connection.finish();
      this.activeConnections.delete(participantId);
      console.log(`🛑 已停止转录 [${participantId}]`);
    }
  }

  async broadcastTranscription(transcription) {
    try {
      console.log("📤 广播转录结果:", {
        participant: transcription.participant,
        text: transcription.text,
        isFinal: transcription.isFinal,
        confidence: transcription.confidence,
      });

      // 在实际应用中，这里应该通过 LiveKit 的数据通道发送到前端
      // 或者通过 WebSocket 直接发送到前端应用
    } catch (error) {
      console.error("❌ 广播转录结果失败:", error);
    }
  }

  getTranscriptionHistory() {
    return this.transcriptionResults;
  }

  getStats() {
    const totalTranscriptions = this.transcriptionResults.length;
    const avgConfidence =
      totalTranscriptions > 0
        ? this.transcriptionResults.reduce((sum, t) => sum + t.confidence, 0) /
          totalTranscriptions
        : 0;

    return {
      totalTranscriptions,
      avgConfidence: (avgConfidence * 100).toFixed(1),
      activeConnections: this.activeConnections.size,
    };
  }
}

async function startTranscriptionAgent() {
  try {
    console.log("🚀 启动实时转录代理...");

    // 创建 RoomService 客户端
    const roomService = new RoomServiceClient(
      process.env.LIVEKIT_URL,
      process.env.LIVEKIT_API_KEY,
      process.env.LIVEKIT_API_SECRET
    );

    // 测试连接
    console.log("📡 测试 LiveKit 服务器连接...");
    const rooms = await roomService.listRooms();
    console.log("✅ 成功连接到 LiveKit 服务器");
    console.log(`📊 当前房间数量: ${rooms.length}`);

    // 创建转录房间
    const roomName = "transcription-room";
    console.log(`🏠 确保房间 "${roomName}" 存在...`);

    try {
      await roomService.createRoom({
        name: roomName,
        emptyTimeout: 300,
        maxParticipants: 10,
      });
      console.log(`✅ 房间 "${roomName}" 已创建或已存在`);
    } catch (error) {
      if (error.message.includes("already exists")) {
        console.log(`✅ 房间 "${roomName}" 已存在`);
      } else {
        throw error;
      }
    }

    // 初始化转录管理器
    const transcriptionManager = new TranscriptionManager(
      roomService,
      roomName
    );
    console.log("🎙️ 转录管理器已初始化");

    // 监听房间参与者变化
    let previousParticipants = new Set();

    const checkParticipants = async () => {
      try {
        const participants = await roomService.listParticipants(roomName);
        const currentParticipants = new Set(
          participants.map((p) => p.identity)
        );

        // 检查新加入的参与者
        for (const participantId of currentParticipants) {
          if (
            !previousParticipants.has(participantId) &&
            participantId !== "transcription-agent"
          ) {
            console.log(`👤 新参与者加入: ${participantId}`);
            await transcriptionManager.startTranscription(participantId);
          }
        }

        // 检查离开的参与者
        for (const participantId of previousParticipants) {
          if (!currentParticipants.has(participantId)) {
            console.log(`👋 参与者离开: ${participantId}`);
            await transcriptionManager.stopTranscription(participantId);
          }
        }

        previousParticipants = currentParticipants;

        if (participants.length > 0) {
          const stats = transcriptionManager.getStats();
          console.log(
            `💓 房间状态: ${participants.length} 个参与者 | 转录统计: ${stats.totalTranscriptions} 条记录, 平均置信度: ${stats.avgConfidence}%`
          );
        } else {
          console.log("📊 房间中暂无参与者");
        }
      } catch (error) {
        if (error.message.includes("does not exist")) {
          console.log("📊 等待房间创建...");
        } else {
          console.log("📊 房间状态检查错误:", error.message);
        }
      }
    };

    // 定期检查参与者状态
    setInterval(checkParticipants, 5000);

    console.log("🚀 实时转录代理已启动并准备就绪！");
    console.log(
      "📱 您现在可以在浏览器中访问 http://localhost:5000 来测试转录功能"
    );
    console.log("🎤 当参与者加入房间并开始说话时，将自动开始转录");
    console.log("📝 转录结果将显示在控制台中");
  } catch (error) {
    console.error("❌ 转录代理启动失败:", error);
    console.error("错误详情:", error.stack);
  }
}

// 启动转录代理
startTranscriptionAgent();
