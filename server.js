import express from "express";
import { AccessToken } from "livekit-server-sdk";
import { config } from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// 加载环境变量
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(express.json());
app.use(express.static("public"));

// API 路由：获取 Deepgram 配置（向后兼容）
app.get("/api/deepgram-config", (req, res) => {
  res.json({
    apiKey: process.env.DEEPGRAM_API_KEY,
  });
});

// API 路由：获取完整配置信息
app.get("/api/config", (req, res) => {
  res.json({
    // Deepgram 基础配置
    apiKey: process.env.DEEPGRAM_API_KEY,

    // 模型组配置
    defaultModelGroup: process.env.DEEPGRAM_DEFAULT_MODEL_GROUP || "nova2",

    // Nova-3 配置
    nova3: {
      model: process.env.DEEPGRAM_MODEL_NOVA3 || "nova-3",
      language: process.env.DEEPGRAM_LANGUAGE_NOVA3 || "multi",
      smartFormat: process.env.DEEPGRAM_SMART_FORMAT_NOVA3 !== "false",
      interimResults: process.env.DEEPGRAM_INTERIM_RESULTS_NOVA3 === "true",
      endpointing: parseInt(process.env.DEEPGRAM_ENDPOINTING_NOVA3) || 2000,
      punctuate: process.env.DEEPGRAM_PUNCTUATE_NOVA3 !== "false",
      profanityFilter: process.env.DEEPGRAM_PROFANITY_FILTER_NOVA3 === "true",
      utterances: process.env.DEEPGRAM_UTTERANCES_NOVA3 !== "false",
      uttSplit: parseFloat(process.env.DEEPGRAM_UTT_SPLIT_NOVA3) || 0.4,
      paragraphs: process.env.DEEPGRAM_PARAGRAPHS_NOVA3 === "true",
      fillerWords: process.env.DEEPGRAM_FILLER_WORDS_NOVA3 === "true",
      redact: process.env.DEEPGRAM_REDACT_NOVA3 === "true",
    },

    // Nova-2 配置
    nova2: {
      model: process.env.DEEPGRAM_MODEL_NOVA2 || "nova-2",
      language: process.env.DEEPGRAM_LANGUAGE_NOVA2 || "en",
      smartFormat: process.env.DEEPGRAM_SMART_FORMAT_NOVA2 !== "false",
      interimResults: process.env.DEEPGRAM_INTERIM_RESULTS_NOVA2 === "true",
      endpointing: parseInt(process.env.DEEPGRAM_ENDPOINTING_NOVA2) || 2500,
      punctuate: process.env.DEEPGRAM_PUNCTUATE_NOVA2 !== "false",
      profanityFilter: process.env.DEEPGRAM_PROFANITY_FILTER_NOVA2 === "true",
      utterances: process.env.DEEPGRAM_UTTERANCES_NOVA2 !== "false",
      uttSplit: parseFloat(process.env.DEEPGRAM_UTT_SPLIT_NOVA2) || 0.4,
      paragraphs: process.env.DEEPGRAM_PARAGRAPHS_NOVA2 === "true",
      fillerWords: process.env.DEEPGRAM_FILLER_WORDS_NOVA2 === "true",
      redact: process.env.DEEPGRAM_REDACT_NOVA2 === "true",
    },
  });
});

// API 路由：生成访问令牌
app.post("/api/token", async (req, res) => {
  try {
    const { roomName, participantName } = req.body;

    if (!roomName || !participantName) {
      return res.status(400).json({
        error: "缺少必要参数: roomName 和 participantName",
      });
    }

    // 创建访问令牌
    const token = new AccessToken(
      process.env.LIVEKIT_API_KEY,
      process.env.LIVEKIT_API_SECRET,
      {
        identity: participantName,
        ttl: "10m", // 10分钟有效期
      }
    );

    // 添加房间权限
    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
    });

    const jwt = await token.toJwt();

    res.json({
      token: jwt,
      url: process.env.LIVEKIT_URL,
    });

    console.log(`为用户 ${participantName} 生成了房间 ${roomName} 的访问令牌`);
  } catch (error) {
    console.error("生成令牌时出错:", error);
    res.status(500).json({
      error: "生成访问令牌失败",
    });
  }
});

// 健康检查端点
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    environment: {
      livekit_url: process.env.LIVEKIT_URL,
      deepgram_configured: !!process.env.DEEPGRAM_API_KEY,
    },
  });
});

// 根路由
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器已启动在端口 ${PORT}`);
  console.log(`📱 前端界面: http://localhost:${PORT}`);
  console.log(`🔗 LiveKit URL: ${process.env.LIVEKIT_URL}`);
  console.log(
    `🎤 Deepgram 配置: ${process.env.DEEPGRAM_API_KEY ? "已配置" : "未配置"}`
  );

  if (!process.env.LIVEKIT_API_KEY || !process.env.LIVEKIT_API_SECRET) {
    console.warn("⚠️  警告: LiveKit API 密钥未配置");
  }

  if (!process.env.DEEPGRAM_API_KEY) {
    console.warn("⚠️  警告: Deepgram API 密钥未配置");
  }
});
