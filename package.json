{"name": "livekit-deepgram-transcription", "version": "1.0.0", "description": "LiveKit + Deepgram 实时音频转文本应用", "main": "src/agent.js", "type": "module", "scripts": {"start": "node src/agent.js start", "dev": "node src/agent.js dev", "connect": "node src/agent.js connect", "serve": "node server.js", "transcription": "node src/transcription-agent.js", "build": "tsc"}, "keywords": ["livekit", "deepgram", "speech-to-text", "transcription", "realtime"], "author": "", "license": "MIT", "dependencies": {"@deepgram/sdk": "^4.11.1", "@livekit/agents": "^0.7.7", "@livekit/agents-plugin-deepgram": "^0.5.0", "@livekit/agents-plugin-silero": "^0.5.0", "@livekit/rtc-node": "^0.13.18", "dotenv": "^16.3.1", "express": "^4.18.2", "livekit-server-sdk": "^2.0.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.20", "@types/node": "^20.8.0", "@types/ws": "^8.5.8", "typescript": "^5.2.2"}}