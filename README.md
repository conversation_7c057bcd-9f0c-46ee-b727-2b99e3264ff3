# LiveKit + Deepgram 实时音频转文本

这是一个基于 LiveKit Agents 和 Deepgram 的实时音频转文本应用，支持麦克风音频的实时转录，显示正在转化的文本和所有转化结果。

## ✨ 功能特性

- 🎤 **实时音频转录**: 使用 Deepgram 进行高质量的语音识别
- 📝 **实时显示**: 显示正在转录的文本和最终转录结果
- 🌐 **Web 界面**: 美观的前端界面，实时显示转录状态
- 📊 **统计信息**: 显示转录数量、平均置信度和会话时长
- 🔄 **多语言支持**: 自动检测语言并进行转录
- 📱 **响应式设计**: 适配桌面和移动设备

## 🛠️ 技术栈

- **LiveKit Agents**: Node.js 版本 + Python 版本
- **语音识别**: Deepgram STT API
- **前端**: HTML5 + JavaScript + LiveKit Client SDK
- **实时通信**: LiveKit WebRTC

## 📋 前置要求

1. **Node.js**: 版本 18 或更高
2. **LiveKit 服务器**: 可以使用 LiveKit Cloud 或自建服务器
3. **Deepgram API 密钥**: 从 [Deepgram](https://deepgram.com/) 获取

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <your-repo-url>
cd livekit-deepgram-transcription
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

复制 `.env.example` 到 `.env` 并填入你的配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# LiveKit 配置
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# Deepgram 配置
DEEPGRAM_API_KEY=your-deepgram-api-key

# 服务器配置
PORT=3000
```

#### LiveKit 配置选项

**选项 1: 使用 LiveKit Cloud**

1. 访问 [LiveKit Cloud](https://cloud.livekit.io/)
2. 创建项目并获取 API 密钥
3. 使用格式: `wss://your-project.livekit.cloud`

### 4. 获取 Deepgram API 密钥

1. 访问 [Deepgram](https://deepgram.com/)
2. 注册账户并获取 API 密钥
3. 将密钥添加到 `.env` 文件

### 5. 启动应用

**方法 1: 分别启动 (推荐)**

```bash
# 终端 1: 启动 Web 服务器
npm run serve

# 终端 2: 启动转录代理
npm run transcription
```

**方法 2: 使用 LiveKit Agents (实验性)**

```bash
# 启动代理 (在一个终端中)
npm run dev

# 启动 Web 服务器 (在另一个终端中)
npm run serve
```

### 6. 访问应用

打开浏览器访问: http://localhost:3000

## 📖 使用说明

1. **连接房间**: 点击"连接房间"按钮
2. **授权麦克风**: 浏览器会请求麦克风权限，请允许
3. **开始说话**: 连接成功后，开始说话即可看到实时转录
4. **查看结果**:
   - 左侧显示正在转录的文本（实时更新）
   - 右侧显示所有转录历史记录
   - 底部显示统计信息

## 🎯 项目结构

```
├── src/
│   └── agent.js          # LiveKit 代理主文件
├── public/
│   ├── index.html        # 前端界面
│   └── app.js           # 前端 JavaScript
├── server.js            # Express 服务器
├── package.json         # 项目配置
├── .env.example         # 环境变量模板
└── README.md           # 项目文档
```

## 🔧 开发命令

```bash
# 启动服务
npm run serve               # 启动 Web 服务器 (端口 5000)
npm run transcription      # 启动转录代理 (推荐)
npm run dev                # 启动 LiveKit Agents 代理 (实验性)

# 连接到指定房间 (LiveKit Agents)
npm run connect -- --room my-room

# 构建项目（如果有 TypeScript）
npm run build

# 健康检查
curl http://localhost:5000/api/health  # 检查 Web 服务器
```

## 🐛 故障排除

### 常见问题

1. **连接失败**

   - 检查 LiveKit 服务器是否运行
   - 验证 API 密钥和密钥是否正确
   - 确保防火墙允许 WebSocket 连接

2. **麦克风权限被拒绝**

   - 确保浏览器允许麦克风访问
   - 在 HTTPS 环境下使用（生产环境）

3. **转录不工作**

   - 检查 Deepgram API 密钥是否有效
   - 确保网络连接正常
   - 查看控制台错误日志

4. **音频质量问题**
   - 检查麦克风设备
   - 确保环境噪音较小
   - 调整音频设置参数

### 调试技巧

1. **查看代理日志**:

   ```bash
   npm run dev  # 开发模式有详细日志
   ```

2. **查看浏览器控制台**: 打开开发者工具查看错误信息

3. **检查网络连接**: 确保能访问 LiveKit 和 Deepgram 服务

## 🔒 安全注意事项

1. **API 密钥安全**:

   - 不要将 `.env` 文件提交到版本控制
   - 在生产环境中使用环境变量

2. **访问控制**:

   - 考虑添加用户认证
   - 限制房间访问权限

3. **HTTPS**:
   - 生产环境必须使用 HTTPS
   - 麦克风访问需要安全上下文

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 [LiveKit 文档](https://docs.livekit.io/)
3. 查看 [Deepgram 文档](https://developers.deepgram.com/)
4. 提交 Issue 到项目仓库

---

**享受实时语音转文本的体验！** 🎉
