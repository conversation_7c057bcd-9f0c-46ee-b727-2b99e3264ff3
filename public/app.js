class TranscriptionApp {
  constructor() {
    this.room = null;
    this.transcriptions = [];
    this.sessionStartTime = null;
    this.sessionTimer = null;

    this.initializeElements();
    this.bindEvents();
  }

  initializeElements() {
    this.connectBtn = document.getElementById("connectBtn");
    this.disconnectBtn = document.getElementById("disconnectBtn");
    this.startTranscriptionBtn = document.getElementById(
      "startTranscriptionBtn"
    );
    this.stopTranscriptionBtn = document.getElementById("stopTranscriptionBtn");
    this.status = document.getElementById("status");
    this.currentTranscription = document.getElementById("currentTranscription");
    this.transcriptionHistory = document.getElementById("transcriptionHistory");
    this.totalCount = document.getElementById("totalCount");
    this.avgConfidence = document.getElementById("avgConfidence");
    this.sessionTime = document.getElementById("sessionTime");
  }

  bindEvents() {
    this.connectBtn.addEventListener("click", () => this.connect());
    this.disconnectBtn.addEventListener("click", () => this.disconnect());
    this.startTranscriptionBtn.addEventListener("click", () =>
      this.startFrontendTranscription()
    );
    this.stopTranscriptionBtn.addEventListener("click", () =>
      this.stopFrontendTranscription()
    );
  }

  async connect() {
    try {
      console.log("🔄 开始连接流程...");
      this.updateStatus("connecting", "正在连接到 LiveKit 房间...");
      this.connectBtn.disabled = true;

      // 检查 LiveKit 客户端是否可用
      if (typeof LivekitClient === "undefined") {
        throw new Error("LiveKit 客户端库未加载");
      }
      console.log("✅ LiveKit 客户端库已加载");
      console.log("LivekitClient API:", Object.keys(LivekitClient));

      // 获取访问令牌
      console.log("🔑 正在获取访问令牌...");
      const response = await fetch("/api/token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roomName: "transcription-room",
          participantName: "user-" + Math.random().toString(36).substr(2, 9),
        }),
      });

      if (!response.ok) {
        throw new Error(
          `获取访问令牌失败: ${response.status} ${response.statusText}`
        );
      }
      console.log("✅ 访问令牌获取成功");

      const { token, url } = await response.json();

      console.log("获取到的连接信息:", {
        token: token ? "已获取" : "未获取",
        url,
      });

      // 连接到房间
      this.room = new LivekitClient.Room();

      // 监听房间事件
      this.room.on(LivekitClient.RoomEvent.Connected, () => {
        this.onConnected();
      });

      this.room.on(LivekitClient.RoomEvent.Disconnected, () => {
        this.onDisconnected();
      });

      this.room.on(
        LivekitClient.RoomEvent.DataReceived,
        (payload, participant) => {
          this.handleDataReceived(payload, participant);
        }
      );

      this.room.on(
        LivekitClient.RoomEvent.ParticipantConnected,
        (participant) => {
          console.log("参与者已连接:", participant.identity);
        }
      );

      // 连接到房间 - 使用服务器返回的正确 URL
      console.log("正在连接到:", url);
      await this.room.connect(url, token);
    } catch (error) {
      console.error("连接失败:", error);
      this.updateStatus("error", `连接失败: ${error.message}`);
      this.connectBtn.disabled = false;
    }
  }

  async onConnected() {
    console.log("已连接到房间");
    this.updateStatus("connected", "已连接 - 请开始说话");
    this.connectBtn.disabled = true;
    this.disconnectBtn.disabled = false;
    this.startTranscriptionBtn.disabled = false;

    this.sessionStartTime = Date.now();
    this.startSessionTimer();

    // 请求麦克风权限并发布音频
    try {
      await this.publishMicrophone();
    } catch (error) {
      console.error("发布麦克风失败:", error);
      this.updateStatus("error", `麦克风权限失败: ${error.message}`);
    }
  }

  async publishMicrophone() {
    console.log("正在发布麦克风...");

    try {
      // 尝试不同的 API 方式
      console.log("尝试发布音频轨道...");

      if (LivekitClient.createLocalAudioTrack) {
        // 方式 1: 使用 createLocalAudioTrack 让它自己处理媒体获取
        console.log("使用 createLocalAudioTrack 方法");
        const localTrack = await LivekitClient.createLocalAudioTrack({
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        });
        await this.room.localParticipant.publishTrack(localTrack);
        console.log("✅ 麦克风已发布 (使用 createLocalAudioTrack)");
      } else {
        // 方式 2: 手动获取媒体流并发布
        console.log("手动获取媒体流");
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
        });

        const audioTrack = stream.getAudioTracks()[0];
        await this.room.localParticipant.publishTrack(audioTrack, {
          name: "microphone",
          source: "microphone",
        });
        console.log("✅ 麦克风已发布 (手动方式)");
      }
    } catch (error) {
      console.error("❌ 发布麦克风失败:", error);

      // 尝试基本的媒体权限检查
      try {
        console.log("🔍 检查媒体权限...");
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        console.log("✅ 媒体权限正常，但 LiveKit 发布失败");
        stream.getTracks().forEach((track) => track.stop());
      } catch (mediaError) {
        console.error("❌ 媒体权限失败:", mediaError);
      }
    }
  }

  onDisconnected() {
    console.log("已断开连接");
    this.updateStatus("connecting", "已断开连接");
    this.connectBtn.disabled = false;
    this.disconnectBtn.disabled = true;
    this.stopSessionTimer();
  }

  async disconnect() {
    if (this.room) {
      await this.room.disconnect();
      this.room = null;
    }
  }

  handleDataReceived(payload, participant) {
    try {
      const data = JSON.parse(new TextDecoder().decode(payload));
      console.log("收到数据:", data);

      switch (data.type) {
        case "interim_transcript":
          this.updateCurrentTranscription(data.text, true);
          break;

        case "final_transcript":
          this.addFinalTranscription(data);
          this.updateCurrentTranscription("等待语音输入...", false);
          break;

        case "status":
          console.log("状态更新:", data.message);
          break;

        case "session_summary":
          console.log("会话总结:", data);
          break;
      }
    } catch (error) {
      console.error("处理数据时出错:", error);
    }
  }

  updateCurrentTranscription(text, isInterim) {
    this.currentTranscription.textContent = text;
    if (isInterim) {
      this.currentTranscription.classList.add("pulse");
    } else {
      this.currentTranscription.classList.remove("pulse");
    }
  }

  addFinalTranscription(data) {
    this.transcriptions.push(data);

    const item = document.createElement("div");
    item.className = "transcription-item";

    const confidenceClass = this.getConfidenceClass(data.confidence);
    const timestamp = new Date(data.timestamp).toLocaleTimeString();

    item.innerHTML = `
            <div class="transcription-text">${data.text}</div>
            <div class="transcription-meta">
                <span>${timestamp}</span>
                <span class="confidence ${confidenceClass}">
                    置信度: ${Math.round(data.confidence * 100)}%
                </span>
            </div>
        `;

    // 如果是第一个转录，清除占位符
    if (this.transcriptions.length === 1) {
      this.transcriptionHistory.innerHTML = "";
    }

    this.transcriptionHistory.insertBefore(
      item,
      this.transcriptionHistory.firstChild
    );
    this.updateStats();
  }

  getConfidenceClass(confidence) {
    if (confidence >= 0.8) return "high";
    if (confidence >= 0.6) return "medium";
    return "low";
  }

  updateStats() {
    this.totalCount.textContent = this.transcriptions.length;

    if (this.transcriptions.length > 0) {
      const avgConfidence =
        this.transcriptions.reduce((sum, t) => sum + t.confidence, 0) /
        this.transcriptions.length;
      this.avgConfidence.textContent = Math.round(avgConfidence * 100) + "%";
    }
  }

  startSessionTimer() {
    this.sessionTimer = setInterval(() => {
      if (this.sessionStartTime) {
        const elapsed = Date.now() - this.sessionStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        this.sessionTime.textContent = `${minutes
          .toString()
          .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
      }
    }, 1000);
  }

  stopSessionTimer() {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  updateStatus(type, message) {
    this.status.className = `status ${type}`;
    this.status.innerHTML =
      type === "connecting"
        ? `<span class="loading"></span> ${message}`
        : message;
  }

  // 前端转录方法
  async startFrontendTranscription() {
    try {
      console.log("🎙️ 启动前端转录...");

      if (window.frontendTranscription) {
        await window.frontendTranscription.startTranscription();
        this.startTranscriptionBtn.disabled = true;
        this.stopTranscriptionBtn.disabled = false;
        this.updateStatus("transcribing", "🎤 正在转录...");
      } else {
        console.error("前端转录模块未加载");
        alert("转录功能暂不可用，请刷新页面重试");
      }
    } catch (error) {
      console.error("启动前端转录失败:", error);
      alert("启动转录失败: " + error.message);
    }
  }

  stopFrontendTranscription() {
    try {
      console.log("🛑 停止前端转录...");

      if (window.frontendTranscription) {
        window.frontendTranscription.stopTranscription();
        this.startTranscriptionBtn.disabled = false;
        this.stopTranscriptionBtn.disabled = true;
        this.updateStatus("connected", "已连接 - 转录已停止");
      }
    } catch (error) {
      console.error("停止前端转录失败:", error);
    }
  }
}

// 初始化应用
document.addEventListener("DOMContentLoaded", () => {
  const app = new TranscriptionApp();
  window.transcriptionApp = app;

  // 初始化模型控制
  initializeModelControls(app);
});

// 初始化模型控制
async function initializeModelControls(app) {
  const modelSelect = document.getElementById("modelSelect");

  const configStatusSpan = document.getElementById("configStatus");

  try {
    // 加载配置
    const response = await fetch("/api/config");

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const config = await response.json();

    // 设置默认模型组
    modelSelect.value = config.defaultModelGroup;
    if (app.transcriptionSystem) {
      app.transcriptionSystem.currentModelGroup = config.defaultModelGroup;
    }

    // 更新配置状态
    configStatusSpan.textContent = `配置: ${config.defaultModelGroup.toUpperCase()} 已加载`;

    console.log("🔧 模型控制已初始化:", config);
  } catch (error) {
    console.error("❌ 配置加载失败:", error);
    configStatusSpan.textContent = "配置: 加载失败";
  }

  // 模型选择事件
  modelSelect.addEventListener("change", (e) => {
    const selectedModel = e.target.value;
    if (app.transcriptionSystem) {
      app.transcriptionSystem.switchModelGroup(selectedModel);
    }
    configStatusSpan.textContent = `配置: ${selectedModel.toUpperCase()} 已选择`;
    console.log(`🔄 用户切换到模型组: ${selectedModel}`);
  });
}
