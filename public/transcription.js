// 前端实时转录功能

class FrontendTranscription {
  constructor() {
    this.isTranscribing = false;
    this.deepgramSocket = null;
    this.mediaRecorder = null;
    this.audioStream = null;

    // 初始化转录系统
    this.currentModelGroup = "nova2"; // 默认使用 Nova-2

    // 转录历史记录（用于去重检查）
    this.transcriptionHistory = [];

    // 加载配置
    this.loadConfiguration();
  }

  // 加载配置（从服务器获取环境变量配置）
  async loadConfiguration() {
    try {
      const response = await fetch("/api/config");

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const config = await response.json();

      this.currentModelGroup = config.defaultModelGroup || "nova2";

      console.log(`🔧 配置已加载: 模型组=${this.currentModelGroup}`);
    } catch (error) {
      console.warn("⚠️ 配置加载失败，使用默认配置:", error);
    }
  }

  // 获取当前模型组的 Deepgram URL
  async getDeepgramUrl() {
    try {
      const response = await fetch("/api/config");

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const config = await response.json();

      const modelGroup = this.currentModelGroup;
      const modelConfig = config[modelGroup];

      if (!modelConfig) {
        throw new Error(`未找到模型组配置: ${modelGroup}`);
      }

      // 使用配置的语言
      const language = modelConfig.language;

      const baseUrl = "wss://api.deepgram.com/v1/listen";
      const params = new URLSearchParams({
        model: modelConfig.model,
        language: language,
        smart_format: modelConfig.smartFormat.toString(),
        interim_results: modelConfig.interimResults.toString(),
        endpointing: modelConfig.endpointing.toString(),
        punctuate: modelConfig.punctuate.toString(),
        profanity_filter: modelConfig.profanityFilter.toString(),
        utterances: modelConfig.utterances.toString(),
        utt_split: modelConfig.uttSplit.toString(),
        paragraphs: modelConfig.paragraphs.toString(),
        filler_words: modelConfig.fillerWords.toString(),
        redact: modelConfig.redact.toString(),
      });

      return `${baseUrl}?${params.toString()}`;
    } catch (error) {
      console.error("获取配置失败，使用默认配置:", error);
      // 回退到默认配置
      return this.getDefaultDeepgramUrl();
    }
  }

  // 默认配置（回退方案）
  getDefaultDeepgramUrl() {
    const modelGroup = this.currentModelGroup;
    const baseUrl = "wss://api.deepgram.com/v1/listen";
    const params = new URLSearchParams({
      model: modelGroup === "nova3" ? "nova-3" : "nova-2",
      language: modelGroup === "nova2" ? "en" : "multi",
      smart_format: "true",
      interim_results: "false",
      endpointing: modelGroup === "nova3" ? "2000" : "2500",
      punctuate: "true",
      profanity_filter: "false",
      utterances: "true",
      utt_split: "0.4",
      paragraphs: "false",
      filler_words: "false",
      redact: "false",
    });

    return `${baseUrl}?${params.toString()}`;
  }

  // 切换模型组
  switchModelGroup(modelGroup) {
    if (modelGroup !== "nova2" && modelGroup !== "nova3") {
      console.error("❌ 无效的模型组:", modelGroup);
      return;
    }

    this.currentModelGroup = modelGroup;
    console.log(`🔄 切换到模型组: ${modelGroup}`);

    // 如果正在转录，重新连接
    if (this.isTranscribing) {
      this.stopTranscription();
      setTimeout(() => this.startTranscription(), 1000);
    }
  }

  async startTranscription() {
    if (this.isTranscribing) {
      console.log("转录已在进行中");
      return;
    }

    try {
      console.log("🎙️ 开始前端转录...");

      // 获取麦克风权限 - 优化音频设置
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000, // 更高的采样率
          channelCount: 1, // 单声道
          volume: 1.0, // 最大音量
        },
      });

      console.log("🎤 音频流设置:", {
        sampleRate: this.audioStream.getAudioTracks()[0].getSettings()
          .sampleRate,
        channelCount: this.audioStream.getAudioTracks()[0].getSettings()
          .channelCount,
      });

      // 连接到 Deepgram
      await this.connectToDeepgram();

      // 设置音频录制
      this.setupAudioRecording();

      this.isTranscribing = true;
      console.log("✅ 前端转录已启动");

      // 更新 UI
      this.updateTranscriptionStatus("🎤 正在转录...");
    } catch (error) {
      console.error("❌ 启动转录失败:", error);
      this.updateTranscriptionStatus("❌ 转录启动失败");
    }
  }

  async connectToDeepgram() {
    return new Promise(async (resolve, reject) => {
      try {
        // 从服务器获取 Deepgram 配置
        const response = await fetch("/api/deepgram-config");
        const config = await response.json();

        if (!config.apiKey) {
          throw new Error("无法获取 Deepgram API 密钥");
        }

        // 使用配置化的 Deepgram URL
        const deepgramUrl = await this.getDeepgramUrl();

        console.log(
          `🔗 连接到 Deepgram (${this.currentModelGroup.toUpperCase()}): ${deepgramUrl}`
        );

        this.deepgramSocket = new WebSocket(deepgramUrl, [
          "token",
          config.apiKey,
        ]);

        this.deepgramSocket.onopen = () => {
          console.log("🔗 Deepgram WebSocket 连接已建立");

          // 发送测试消息来验证连接
          setTimeout(() => {
            console.log("📡 Deepgram 连接就绪，等待音频数据");
          }, 1000);

          resolve();
        };

        this.deepgramSocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleTranscriptionResult(data);
          } catch (error) {
            console.error("解析转录结果失败:", error);
          }
        };

        this.deepgramSocket.onerror = (error) => {
          console.error("❌ Deepgram WebSocket 错误:", error);
          reject(error);
        };

        this.deepgramSocket.onclose = () => {
          console.log("🔌 Deepgram WebSocket 连接已关闭");
          this.isTranscribing = false;
        };
      } catch (error) {
        console.error("连接 Deepgram 失败:", error);
        reject(error);
      }
    });
  }

  setupAudioRecording() {
    // 尝试不同的音频格式
    const mimeTypes = [
      "audio/webm;codecs=opus",
      "audio/webm",
      "audio/mp4",
      "audio/ogg;codecs=opus",
    ];

    let selectedMimeType = null;
    for (const mimeType of mimeTypes) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        selectedMimeType = mimeType;
        break;
      }
    }

    console.log(`🎵 使用音频格式: ${selectedMimeType}`);

    // 创建 MediaRecorder 来捕获音频 - 优化设置减少单词丢失
    this.mediaRecorder = new MediaRecorder(this.audioStream, {
      mimeType: selectedMimeType,
      audioBitsPerSecond: 128000, // 保持高质量
    });

    this.mediaRecorder.ondataavailable = (event) => {
      if (
        event.data.size > 0 &&
        this.deepgramSocket &&
        this.deepgramSocket.readyState === WebSocket.OPEN
      ) {
        // console.log(`🔊 发送音频数据: ${event.data.size} 字节`);

        // 检查音频数据大小
        if (event.data.size < 2000) {
          console.warn(
            `⚠️ 音频数据较小: ${event.data.size} 字节，可能影响识别质量`
          );
        }

        // 统计音频数据
        if (!this.audioStats) {
          this.audioStats = { totalChunks: 0, totalBytes: 0, avgSize: 0 };
        }
        this.audioStats.totalChunks++;
        this.audioStats.totalBytes += event.data.size;
        this.audioStats.avgSize = Math.round(
          this.audioStats.totalBytes / this.audioStats.totalChunks
        );

        // if (this.audioStats.totalChunks % 50 === 0) {
        //   console.log(
        //     `📊 音频统计: 已发送 ${this.audioStats.totalChunks} 块，总计 ${this.audioStats.totalBytes} 字节，平均 ${this.audioStats.avgSize} 字节/块`
        //   );
        // }

        // 发送音频数据到 Deepgram
        this.deepgramSocket.send(event.data);
      } else if (event.data.size === 0) {
        console.warn("⚠️ 收到空的音频数据");
      }
    };

    this.mediaRecorder.onerror = (event) => {
      console.error("❌ MediaRecorder 错误:", event.error);
    };

    // 每 250ms 发送一次音频数据 (平衡实时性和完整性)
    this.mediaRecorder.start(250);
    console.log("✅ 音频录制已开始");
  }

  handleTranscriptionResult(data) {
    // console.log(`🔍 收到 Deepgram 原始数据:`, JSON.stringify(data, null, 2));

    if (
      data.channel &&
      data.channel.alternatives &&
      data.channel.alternatives[0]
    ) {
      const result = data.channel.alternatives[0];

      if (result.transcript) {
        const transcription = {
          text: result.transcript,
          confidence: result.confidence || 0,
          isFinal: data.is_final || false,
          speechFinal: data.speech_final || false,
          language: this.currentModelGroup === "nova2" ? "en" : "multi",
          timestamp: Date.now() + Math.random(), // 确保时间戳唯一性
        };

        const statusText = transcription.isFinal
          ? "最终"
          : transcription.speechFinal
          ? "语音结束"
          : "临时";
        console.log(
          `📝 ${statusText}转录 [${transcription.language}]: ${
            transcription.text
          } (置信度: ${(transcription.confidence * 100).toFixed(1)}%)`
        );
        console.log(
          `🔍 转录详情: isFinal=${transcription.isFinal}, speechFinal=${transcription.speechFinal}, language=${transcription.language}, text长度=${transcription.text.length}`
        );

        // 更新 UI
        this.displayTranscription(transcription);

        // 发送到 LiveKit 房间（如果需要）
        this.broadcastToRoom(transcription);
      } else {
        // console.log(`⚠️ 转录结果无文本内容:`, result);
      }
    } else {
      console.log(`⚠️ 无效的转录数据结构:`, data);
    }
  }

  displayTranscription(transcription) {
    // 更新当前转录显示
    const currentTranscriptionDiv = document.getElementById(
      "currentTranscription"
    );
    if (currentTranscriptionDiv && !transcription.isFinal) {
      // 显示临时转录
      currentTranscriptionDiv.innerHTML = `
        ${transcription.text}
        <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
          置信度: ${(transcription.confidence * 100).toFixed(1)}%
        </div>
      `;
    }

    // 更新转录历史
    const transcriptionHistoryDiv = document.getElementById(
      "transcriptionHistory"
    );

    console.log(
      `🔍 转录历史检查: isFinal=${
        transcription.isFinal
      }, text="${transcription.text.trim()}", historyDiv存在=${!!transcriptionHistoryDiv}`
    );

    // 简化保存条件 - 主要依赖 Deepgram 的内置优化
    const shouldSave =
      transcription.isFinal || // 最终结果
      transcription.speechFinal; // 语音自然结束

    // 直接使用最简单的去重检查
    const isDuplicate = this.checkForDuplicates(
      transcriptionHistoryDiv,
      transcription.text.trim()
    );

    console.log(
      `🔍 去重检查结果: isDuplicate=${isDuplicate}, text="${transcription.text.trim()}"`
    );

    // 如果检测到重复，记录详细信息
    if (isDuplicate) {
      console.log(`❌ 重复内容被拒绝: "${transcription.text.trim()}"`);
    }

    // 简化逻辑：只检查去重，不进行复杂合并
    // const shouldMerge = this.checkForMerging(
    //   transcriptionHistoryDiv,
    //   transcription.text.trim()
    // );

    // 检查条件
    if (transcriptionHistoryDiv && shouldSave && !isDuplicate) {
      // 清除"暂无转录记录"提示
      const noRecordsMsg = transcriptionHistoryDiv.querySelector(
        '[style*="text-align: center"]'
      );
      if (noRecordsMsg) {
        noRecordsMsg.remove();
      }

      const resultElement = document.createElement("div");
      resultElement.className = "transcription-item";
      resultElement.style.cssText = `
        padding: 10px;
        margin: 5px 0;
        background: #f5f5f5;
        border-radius: 5px;
        border-left: 4px solid ${
          transcription.confidence > 0.8
            ? "#4CAF50"
            : transcription.confidence > 0.5
            ? "#FF9800"
            : "#F44336"
        };
      `;
      resultElement.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">${
          transcription.text
        }</div>
        <div style="font-size: 0.8em; color: #666;">
          <span>${new Date(transcription.timestamp).toLocaleTimeString()}</span>
          <span style="margin-left: 10px;">语言: ${
            transcription.language || "unknown"
          }</span>
          <span style="margin-left: 10px;">置信度: ${(
            transcription.confidence * 100
          ).toFixed(1)}%</span>
        </div>
      `;

      transcriptionHistoryDiv.appendChild(resultElement);
      // console.log(`✅ 添加转录记录到历史: "${transcription.text}"`);

      // 限制历史记录数量，避免过多内容
      const maxHistoryItems = 20;
      const historyItems = transcriptionHistoryDiv.querySelectorAll(
        ".transcription-item"
      );
      // console.log(`📊 当前历史记录数量: ${historyItems.length}`);

      if (historyItems.length > maxHistoryItems) {
        // 删除最旧的记录
        const removedCount = historyItems.length - maxHistoryItems;
        for (let i = 0; i < removedCount; i++) {
          historyItems[i].remove();
        }
        // console.log(`🗑️ 删除了 ${removedCount} 条旧记录`);
      }

      // 标记新添加的元素
      resultElement.setAttribute("data-new-item", "true");

      // 强制滚动到底部
      const forceScrollToBottom = () => {
        transcriptionHistoryDiv.scrollTop =
          transcriptionHistoryDiv.scrollHeight;

        // console.log(
        //   `📜 滚动状态: scrollHeight=${scrollHeight}, clientHeight=${clientHeight}, scrollTop=${transcriptionHistoryDiv.scrollTop}`
        // );

        // 使用多种方法确保滚动到底部
        const lastItem = transcriptionHistoryDiv.lastElementChild;
        if (lastItem) {
          // 方法1: scrollIntoView
          lastItem.scrollIntoView({ behavior: "instant", block: "end" });

          // 方法2: 直接设置scrollTop到最大值
          transcriptionHistoryDiv.scrollTop =
            transcriptionHistoryDiv.scrollHeight;

          // console.log(`🔄 多重滚动方法执行完成`);
        }
      };

      // 立即执行一次
      forceScrollToBottom();

      // 延迟执行确保DOM更新完成
      setTimeout(forceScrollToBottom, 50);
      setTimeout(forceScrollToBottom, 200);

      // 清除当前转录显示
      if (currentTranscriptionDiv) {
        currentTranscriptionDiv.innerHTML = "等待语音输入...";
      }
    }
  }

  broadcastToRoom(transcription) {
    // 如果有 LiveKit 房间连接，发送转录结果
    if (window.transcriptionApp && window.transcriptionApp.room) {
      try {
        const data = JSON.stringify({
          type: "transcription",
          ...transcription,
        });

        window.transcriptionApp.room.localParticipant.publishData(
          new TextEncoder().encode(data),
          { reliable: true }
        );
      } catch (error) {
        console.error("发送转录结果到房间失败:", error);
      }
    }
  }

  // 最简单直接的去重检查 - 恢复到基础版本
  checkForDuplicates(historyDiv, newText) {
    if (!historyDiv || !newText) return false;

    const existingItems = historyDiv.querySelectorAll(".transcription-item");
    const recentItems = Array.from(existingItems).slice(-3); // 只检查最近3条记录

    for (const item of recentItems) {
      const existingText = item.querySelector("div").textContent.trim();

      // 只进行完全相同检查
      if (existingText === newText) {
        console.log(`🔄 发现完全重复: "${newText}"`);
        return true;
      }
    }

    return false; // 不是重复，允许添加
  }

  // 检查是否是连续的句子片段
  isLikelyContinuation(text1, text2) {
    // 1. 如果两个文本的开头或结尾有明显的语义连接
    const text1Lower = text1.toLowerCase();
    const text2Lower = text2.toLowerCase();

    // 2. 检查是否是同一个句子的不同部分
    // 例如: "To grow and to build" vs "grow, and to build"
    const text1Words = text1Lower.split(/\s+/);
    const text2Words = text2Lower.split(/\s+/);

    // 如果第二个文本的开头词汇在第一个文本的结尾部分出现
    const lastFewWords = text1Words.slice(-3); // 取最后3个词
    const firstFewWords = text2Words.slice(0, 3); // 取前3个词

    const overlap = lastFewWords.filter((word) => firstFewWords.includes(word));

    // 如果有2个或以上的词汇重叠，可能是连续句子
    if (overlap.length >= 2) {
      return true;
    }

    // 3. 检查是否是明显的句子延续模式
    // 第一个文本以逗号结尾，第二个文本是小写开头
    if (
      text1.endsWith(",") &&
      text2.charAt(0).toLowerCase() === text2.charAt(0)
    ) {
      return true;
    }

    return false;
  }

  // 检查是否应该合并句子片段
  checkForMerging(historyDiv, newText) {
    if (!historyDiv || !newText) return false;

    const existingItems = historyDiv.querySelectorAll(".transcription-item");
    const lastItem = existingItems[existingItems.length - 1];

    if (!lastItem) return false;

    const lastText = lastItem.querySelector("div").textContent.trim();

    // 检查是否可以合并（语义连接）
    const canMerge = this.shouldMergeTexts(lastText, newText);

    if (canMerge) {
      const mergedText = this.mergeTexts(lastText, newText);
      // console.log(
      //   `🔗 合并句子: "${lastText}" + "${newText}" = "${mergedText}"`
      // );

      // 更新最后一个元素的内容
      const textDiv = lastItem.querySelector("div");
      textDiv.textContent = mergedText;

      return true; // 已合并，不需要添加新元素
    }

    return false;
  }

  // 判断两个文本是否应该合并 - 保持短句子策略
  shouldMergeTexts(text1, text2) {
    // 1. 如果第一个文本已经中等长度（>50字符），不合并
    if (text1.length > 50) {
      return false;
    }

    // 2. 如果第一个文本以完整句子结尾，不合并
    if (text1.match(/[.!?]$/) && text1.length > 15) {
      return false;
    }

    // 3. 只合并很短的不完整文本
    if (text1.length < 15) {
      return true; // 很短的文本需要合并
    }

    // 4. 检查第一个文本是否以逗号结尾（明显不完整）
    if (text1.endsWith(",")) {
      return true;
    }

    // 5. 检查第二个文本是否以小写开头且很短（可能是延续）
    if (
      text2.charAt(0).toLowerCase() === text2.charAt(0) &&
      text2.charAt(0).match(/[a-z]/) &&
      text2.length < 30
    ) {
      return true;
    }

    // 6. 默认不合并，保持短句子
    return false;
  }

  // 合并两个文本 - 智能处理各种情况
  mergeTexts(text1, text2) {
    // 移除文本末尾和开头的多余空格
    text1 = text1.trim();
    text2 = text2.trim();

    // 如果第一个文本以标点符号结尾（除了句号），直接用空格连接
    if (text1.match(/[,;:!?—-]$/)) {
      return `${text1} ${text2}`;
    }

    // 如果第一个文本以句号结尾，但很短，可能是缩写，用空格连接
    if (text1.endsWith(".") && text1.length < 50) {
      return `${text1} ${text2}`;
    }

    // 如果第二个文本是小写开头或以连接词开头，直接连接
    if (
      text2.charAt(0).toLowerCase() === text2.charAt(0) ||
      ["and", "but", "or", "so", "to", "for", "with"].includes(
        text2.split(" ")[0].toLowerCase()
      )
    ) {
      return `${text1} ${text2}`;
    }

    // 如果第一个文本不以任何标点结尾，可能需要添加适当的连接
    if (!text1.match(/[.!?,:;—-]$/)) {
      return `${text1} ${text2}`;
    }

    // 默认情况：用空格连接
    return `${text1} ${text2}`;
  }

  // 相似度计算（统一方法）
  calculateSimilarity(text1, text2) {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    const intersection = new Set([...words1].filter((x) => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    return intersection.size / union.size;
  }

  // 清空去重历史（当停止转录时）
  clearDeduplicationHistory() {
    this.transcriptionHistory = [];
    console.log("🧹 已清空转录历史");
  }

  stopTranscription() {
    console.log("🛑 停止转录...");

    if (this.mediaRecorder && this.mediaRecorder.state !== "inactive") {
      this.mediaRecorder.stop();
    }

    if (this.deepgramSocket) {
      this.deepgramSocket.close();
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach((track) => track.stop());
    }

    // 清空去重历史
    this.clearDeduplicationHistory();

    this.isTranscribing = false;
    this.updateTranscriptionStatus("⏹️ 转录已停止");
    console.log("✅ 转录已停止");
  }

  updateTranscriptionStatus(status) {
    const statusElement = document.getElementById("transcription-status");
    if (statusElement) {
      statusElement.textContent = status;
    }
  }
}

// 全局实例
window.frontendTranscription = new FrontendTranscription();
