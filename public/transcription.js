// 前端实时转录功能

class FrontendTranscription {
  constructor() {
    this.isTranscribing = false;
    this.deepgramSocket = null;
    this.mediaRecorder = null;
    this.audioStream = null;

    // 初始化转录系统
    this.currentModelGroup = "nova2"; // 默认使用 Nova-2

    // 转录历史记录（用于去重检查）
    this.transcriptionHistory = [];

    // 加载配置
    this.loadConfiguration();
  }

  // 加载配置（从服务器获取环境变量配置）
  async loadConfiguration() {
    try {
      const response = await fetch("/api/config");

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const config = await response.json();

      this.currentModelGroup = config.defaultModelGroup || "nova2";

      console.log(`🔧 配置已加载: 模型组=${this.currentModelGroup}`);
    } catch (error) {
      console.warn("⚠️ 配置加载失败，使用默认配置:", error);
    }
  }

  // 获取 Deepgram WebSocket URL（从服务器配置）
  async getDeepgramUrl() {
    try {
      const response = await fetch("/api/config");
      const config = await response.json();

      // 根据当前模型组选择配置
      const modelConfig = config[this.currentModelGroup];

      if (!modelConfig) {
        console.warn(
          `⚠️ 未找到模型组 ${this.currentModelGroup} 的配置，使用默认配置`
        );
        return this.getDefaultDeepgramUrl();
      }

      // 使用配置的语言
      const language = modelConfig.language;

      const baseUrl = "wss://api.deepgram.com/v1/listen";
      const params = new URLSearchParams({
        model: modelConfig.model,
        language: language,
        smart_format: modelConfig.smartFormat.toString(),
        interim_results: modelConfig.interimResults.toString(),
        endpointing: modelConfig.endpointing.toString(),
        punctuate: modelConfig.punctuate.toString(),
        profanity_filter: modelConfig.profanityFilter.toString(),
        utterances: modelConfig.utterances.toString(),
        utt_split: modelConfig.uttSplit.toString(),
        paragraphs: modelConfig.paragraphs.toString(),
        filler_words: modelConfig.fillerWords.toString(),
        redact: modelConfig.redact.toString(),
      });

      return `${baseUrl}?${params.toString()}`;
    } catch (error) {
      console.error("获取配置失败，使用默认配置:", error);
      // 回退到默认配置
      return this.getDefaultDeepgramUrl();
    }
  }

  // 默认配置（回退方案）
  getDefaultDeepgramUrl() {
    const modelGroup = this.currentModelGroup;
    const baseUrl = "wss://api.deepgram.com/v1/listen";
    const params = new URLSearchParams({
      model: modelGroup === "nova3" ? "nova-3" : "nova-2",
      language: modelGroup === "nova2" ? "en" : "multi",
      smart_format: "true",
      interim_results: "false",
      endpointing: modelGroup === "nova3" ? "2000" : "2500",
      punctuate: "true",
      profanity_filter: "false",
      utterances: "true",
      utt_split: "0.4",
      paragraphs: "false",
      filler_words: "false",
      redact: "false",
    });

    return `${baseUrl}?${params.toString()}`;
  }

  // 切换模型组
  switchModelGroup(modelGroup) {
    if (modelGroup !== "nova2" && modelGroup !== "nova3") {
      console.error("❌ 无效的模型组:", modelGroup);
      return;
    }

    this.currentModelGroup = modelGroup;
    console.log(`🔄 切换到模型组: ${modelGroup}`);

    // 如果正在转录，重新连接
    if (this.isTranscribing) {
      this.stopTranscription();
      setTimeout(() => this.startTranscription(), 1000);
    }
  }

  async startTranscription() {
    if (this.isTranscribing) {
      console.log("转录已在进行中");
      return;
    }

    try {
      console.log("🎙️ 开始前端转录...");

      // 获取麦克风权限
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          channelCount: 1,
          volume: 1.0,
        },
      });

      console.log("🎤 音频流设置:", {
        sampleRate: this.audioStream.getAudioTracks()[0].getSettings()
          .sampleRate,
        channelCount: this.audioStream.getAudioTracks()[0].getSettings()
          .channelCount,
      });

      // 连接到 Deepgram
      await this.connectToDeepgram();

      // 设置音频录制
      this.setupAudioRecording();

      this.isTranscribing = true;
      console.log("✅ 前端转录已启动");

      // 更新 UI
      this.updateTranscriptionStatus("🎤 正在转录...");
    } catch (error) {
      console.error("❌ 启动转录失败:", error);
      this.updateTranscriptionStatus("❌ 转录启动失败");
    }
  }

  async connectToDeepgram() {
    return new Promise(async (resolve, reject) => {
      try {
        // 从服务器获取 Deepgram 配置
        const response = await fetch("/api/deepgram-config");
        const config = await response.json();

        if (!config.apiKey) {
          throw new Error("无法获取 Deepgram API 密钥");
        }

        // 使用配置化的 Deepgram URL
        const deepgramUrl = await this.getDeepgramUrl();

        console.log(
          `🔗 连接到 Deepgram (${this.currentModelGroup.toUpperCase()}): ${deepgramUrl}`
        );

        this.deepgramSocket = new WebSocket(deepgramUrl, [
          "token",
          config.apiKey,
        ]);

        this.deepgramSocket.onopen = () => {
          console.log("🔗 Deepgram WebSocket 连接已建立");

          // 发送测试消息来验证连接
          setTimeout(() => {
            console.log("📡 Deepgram 连接就绪，等待音频数据");
          }, 1000);

          resolve();
        };

        this.deepgramSocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleTranscriptionResult(data);
          } catch (error) {
            console.error("解析转录结果失败:", error);
          }
        };

        this.deepgramSocket.onerror = (error) => {
          console.error("❌ Deepgram WebSocket 错误:", error);
          reject(error);
        };

        this.deepgramSocket.onclose = () => {
          console.log("🔌 Deepgram WebSocket 连接已关闭");
          this.isTranscribing = false;
        };
      } catch (error) {
        console.error("连接 Deepgram 失败:", error);
        reject(error);
      }
    });
  }

  setupAudioRecording() {
    // 尝试不同的音频格式
    const mimeTypes = [
      "audio/webm;codecs=opus",
      "audio/webm",
      "audio/mp4",
      "audio/ogg;codecs=opus",
    ];

    let selectedMimeType = null;
    for (const mimeType of mimeTypes) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        selectedMimeType = mimeType;
        break;
      }
    }

    console.log(`🎵 使用音频格式: ${selectedMimeType}`);

    // 创建 MediaRecorder 来捕获音频
    this.mediaRecorder = new MediaRecorder(this.audioStream, {
      mimeType: selectedMimeType,
      audioBitsPerSecond: 128000,
    });

    this.mediaRecorder.ondataavailable = (event) => {
      if (
        event.data.size > 0 &&
        this.deepgramSocket &&
        this.deepgramSocket.readyState === WebSocket.OPEN
      ) {
        // 发送音频数据到 Deepgram
        this.deepgramSocket.send(event.data);
      }
    };

    this.mediaRecorder.onerror = (event) => {
      console.error("❌ MediaRecorder 错误:", event.error);
    };

    // 每 250ms 发送一次音频数据
    this.mediaRecorder.start(250);
    console.log("✅ 音频录制已开始");
  }

  handleTranscriptionResult(data) {
    if (
      data.channel &&
      data.channel.alternatives &&
      data.channel.alternatives[0]
    ) {
      const result = data.channel.alternatives[0];

      if (result.transcript) {
        const transcription = {
          text: result.transcript,
          confidence: result.confidence || 0,
          isFinal: data.is_final || false,
          speechFinal: data.speech_final || false,
          language: this.currentModelGroup === "nova2" ? "en" : "multi",
          timestamp: Date.now(),
        };

        console.log(
          `📝 ${transcription.isFinal ? "最终" : "临时"}转录 [${
            transcription.language
          }]: ${transcription.text} (置信度: ${(
            transcription.confidence * 100
          ).toFixed(1)}%)`
        );

        // 更新 UI
        this.displayTranscription(transcription);

        // 发送到 LiveKit 房间（如果需要）
        this.broadcastToRoom(transcription);
      }
    }
  }

  displayTranscription(transcription) {
    // 更新当前转录显示
    const currentTranscriptionDiv = document.getElementById(
      "currentTranscription"
    );
    if (currentTranscriptionDiv && !transcription.isFinal) {
      // 显示临时转录
      currentTranscriptionDiv.innerHTML = `
        ${transcription.text}
        <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
          置信度: ${(transcription.confidence * 100).toFixed(1)}%
        </div>
      `;
    }

    // 更新转录历史
    const transcriptionHistoryDiv = document.getElementById(
      "transcriptionHistory"
    );

    // 简化保存条件 - 只保存最终结果
    const shouldSave = transcription.isFinal;

    if (transcriptionHistoryDiv && shouldSave) {
      // 清除"暂无转录记录"提示
      const noRecordsMsg = transcriptionHistoryDiv.querySelector(
        '[style*="text-align: center"]'
      );
      if (noRecordsMsg) {
        noRecordsMsg.remove();
      }

      const resultElement = document.createElement("div");
      resultElement.className = "transcription-item";
      resultElement.style.cssText = `
        padding: 10px;
        margin: 5px 0;
        background: #f5f5f5;
        border-radius: 5px;
        border-left: 4px solid ${
          transcription.confidence > 0.8
            ? "#4CAF50"
            : transcription.confidence > 0.5
            ? "#FF9800"
            : "#F44336"
        };
      `;
      resultElement.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">${
          transcription.text
        }</div>
        <div style="font-size: 0.8em; color: #666;">
          <span>${new Date(transcription.timestamp).toLocaleTimeString()}</span>
          <span style="margin-left: 10px;">语言: ${
            transcription.language || "unknown"
          }</span>
          <span style="margin-left: 10px;">置信度: ${(
            transcription.confidence * 100
          ).toFixed(1)}%</span>
        </div>
      `;

      transcriptionHistoryDiv.appendChild(resultElement);

      // 自动滚动到底部
      transcriptionHistoryDiv.scrollTop = transcriptionHistoryDiv.scrollHeight;

      // 清除当前转录显示
      if (currentTranscriptionDiv) {
        currentTranscriptionDiv.innerHTML = "等待语音输入...";
      }
    }
  }

  broadcastToRoom(transcription) {
    // 如果有 LiveKit 房间连接，发送转录结果
    if (window.transcriptionApp && window.transcriptionApp.room) {
      try {
        const data = JSON.stringify({
          type: "transcription",
          ...transcription,
        });

        window.transcriptionApp.room.localParticipant.publishData(
          new TextEncoder().encode(data),
          { reliable: true }
        );
      } catch (error) {
        console.error("发送转录结果到房间失败:", error);
      }
    }
  }

  stopTranscription() {
    console.log("🛑 停止转录...");

    if (this.mediaRecorder && this.mediaRecorder.state !== "inactive") {
      this.mediaRecorder.stop();
    }

    if (this.deepgramSocket) {
      this.deepgramSocket.close();
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach((track) => track.stop());
    }

    this.isTranscribing = false;
    this.updateTranscriptionStatus("⏹️ 转录已停止");
    console.log("✅ 转录已停止");
  }

  updateTranscriptionStatus(status) {
    const statusElement = document.getElementById("transcription-status");
    if (statusElement) {
      statusElement.textContent = status;
    }
  }
}

// 全局实例
window.frontendTranscription = new FrontendTranscription();
