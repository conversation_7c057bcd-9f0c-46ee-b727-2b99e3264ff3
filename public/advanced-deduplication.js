// 高级去重解决方案
// 使用多种算法进行智能去重

class AdvancedDeduplication {
  constructor() {
    this.history = [];
    this.maxHistory = 20;
  }

  // Levenshtein 距离算法
  levenshteinDistance(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len2; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= len1; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= len2; i++) {
      for (let j = 1; j <= len1; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[len2][len1];
  }

  // 计算相似度百分比
  calculateSimilarity(str1, str2) {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1.0;
    
    const distance = this.levenshteinDistance(str1, str2);
    return (maxLength - distance) / maxLength;
  }

  // Jaccard 相似度
  jaccardSimilarity(str1, str2) {
    const set1 = new Set(str1.toLowerCase().split(/\s+/));
    const set2 = new Set(str2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  // 检查是否应该添加新的转录
  shouldAdd(newText, confidence, isFinal) {
    newText = newText.trim();
    
    // 基本过滤条件
    if (!newText || newText.length < 3) {
      return { shouldAdd: false, reason: "文本太短" };
    }

    // 检查与历史记录的重复
    for (let i = this.history.length - 1; i >= Math.max(0, this.history.length - 5); i--) {
      const existing = this.history[i];
      
      // 完全相同
      if (existing.text === newText) {
        return { shouldAdd: false, reason: "完全重复" };
      }

      // 包含关系检查
      if (newText.includes(existing.text) && newText.length > existing.text.length) {
        // 新文本包含旧文本，替换旧文本
        this.history.splice(i, 1);
        return { shouldAdd: true, reason: "扩展替换", replaced: existing.text };
      }

      if (existing.text.includes(newText) && existing.text.length > newText.length) {
        return { shouldAdd: false, reason: "被包含重复" };
      }

      // 高相似度检查
      const similarity = this.calculateSimilarity(existing.text, newText);
      if (similarity > 0.85) {
        // 保留更长或置信度更高的版本
        if (newText.length > existing.text.length || confidence > existing.confidence) {
          this.history.splice(i, 1);
          return { shouldAdd: true, reason: "相似度替换", similarity: similarity };
        } else {
          return { shouldAdd: false, reason: "相似度重复", similarity: similarity };
        }
      }

      // Jaccard 相似度检查
      const jaccard = this.jaccardSimilarity(existing.text, newText);
      if (jaccard > 0.8) {
        if (newText.length > existing.text.length) {
          this.history.splice(i, 1);
          return { shouldAdd: true, reason: "Jaccard替换", jaccard: jaccard };
        } else {
          return { shouldAdd: false, reason: "Jaccard重复", jaccard: jaccard };
        }
      }
    }

    // 质量过滤
    if (!isFinal && confidence < 0.9 && newText.length < 10) {
      return { shouldAdd: false, reason: "质量不足" };
    }

    return { shouldAdd: true, reason: "通过检查" };
  }

  // 添加新的转录记录
  addTranscription(text, confidence, isFinal, speechFinal) {
    const result = this.shouldAdd(text, confidence, isFinal || speechFinal);
    
    if (result.shouldAdd) {
      const record = {
        text: text.trim(),
        confidence: confidence,
        isFinal: isFinal,
        speechFinal: speechFinal,
        timestamp: Date.now()
      };
      
      this.history.push(record);
      
      // 限制历史记录数量
      if (this.history.length > this.maxHistory) {
        this.history.shift();
      }
      
      console.log(`✅ 添加转录: "${text}" (${result.reason})`);
      return { added: true, record: record, reason: result.reason };
    } else {
      console.log(`❌ 拒绝转录: "${text}" (${result.reason})`);
      return { added: false, reason: result.reason };
    }
  }

  // 获取历史记录
  getHistory() {
    return this.history;
  }

  // 清空历史记录
  clear() {
    this.history = [];
  }
}

// 导出供使用
window.AdvancedDeduplication = AdvancedDeduplication;
