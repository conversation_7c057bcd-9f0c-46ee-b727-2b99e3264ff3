<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LiveKit + Deepgram 实时音频转文本</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .main-content {
        padding: 40px 30px;
      }

      .controls {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }

      .model-controls {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
        border: 1px solid #e9ecef;
      }

      .model-group,
      .config-info {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .model-select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background: white;
        font-size: 14px;
        min-width: 180px;
      }

      .config-status {
        background: #f3e5f5;
        color: #7b1fa2;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
      }

      .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 150px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .btn-danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
      }

      .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .status {
        text-align: center;
        padding: 20px;
        margin-bottom: 30px;
        border-radius: 10px;
        font-weight: bold;
      }

      .status.connecting {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }

      .status.connected {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .transcription-area {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-top: 30px;
      }

      @media (max-width: 768px) {
        .transcription-area {
          grid-template-columns: 1fr;
        }
      }

      .transcription-panel {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        min-height: 400px;
      }

      .transcription-panel h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 1.3em;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .current-transcription {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        border-radius: 5px;
        font-size: 1.1em;
        min-height: 60px;
        display: flex;
        align-items: center;
        font-style: italic;
        color: #1976d2;
      }

      .transcription-history {
        height: 300px;
        min-height: 300px;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        padding: 10px;
        scroll-behavior: smooth;
        background: #fafafa;
        box-sizing: border-box;
      }

      .transcription-item {
        background: white;
        margin-bottom: 10px;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #4caf50;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .transcription-text {
        font-size: 1em;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .transcription-meta {
        font-size: 0.85em;
        color: #666;
        display: flex;
        justify-content: space-between;
      }

      .confidence {
        font-weight: bold;
      }

      .confidence.high {
        color: #4caf50;
      }
      .confidence.medium {
        color: #ff9800;
      }
      .confidence.low {
        color: #f44336;
      }

      .stats {
        display: flex;
        justify-content: space-around;
        background: #f0f0f0;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
      }

      .stat-item {
        text-align: center;
      }

      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #667eea;
      }

      .stat-label {
        color: #666;
        font-size: 0.9em;
      }

      .loading {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .pulse {
        animation: pulse 1.5s ease-in-out infinite alternate;
      }

      @keyframes pulse {
        from {
          opacity: 0.6;
        }
        to {
          opacity: 1;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="main-content">
        <div class="controls">
          <button id="connectBtn" class="btn btn-primary">连接房间</button>
          <button id="disconnectBtn" class="btn btn-danger" disabled>
            断开连接
          </button>
          <button id="startTranscriptionBtn" class="btn btn-success" disabled>
            开始转录
          </button>
          <button id="stopTranscriptionBtn" class="btn btn-danger" disabled>
            停止转录
          </button>
        </div>

        <!-- 模型配置控制 -->
        <div class="model-controls">
          <div class="model-group">
            <label for="modelSelect">模型选择:</label>
            <select id="modelSelect" class="model-select">
              <option value="nova2">Nova-2 (英语优化)</option>
              <option value="nova3">Nova-3 (多语言)</option>
            </select>
          </div>

          <div class="config-info">
            <span id="configStatus" class="config-status">配置: 加载中...</span>
          </div>
        </div>

        <div id="status" class="status connecting">
          <span class="loading"></span> 准备连接...
        </div>

        <div class="transcription-area">
          <div class="transcription-panel">
            <h3>📝 正在转录</h3>
            <div id="currentTranscription" class="current-transcription">
              等待语音输入...
            </div>
          </div>

          <div class="transcription-panel">
            <h3>📋 转录历史</h3>
            <div id="transcriptionHistory" class="transcription-history">
              <div style="text-align: center; color: #999; padding: 20px">
                暂无转录记录
              </div>
            </div>
          </div>
        </div>

        <div class="stats">
          <div class="stat-item">
            <div id="totalCount" class="stat-number">0</div>
            <div class="stat-label">总转录数</div>
          </div>
          <div class="stat-item">
            <div id="avgConfidence" class="stat-number">0%</div>
            <div class="stat-label">平均置信度</div>
          </div>
          <div class="stat-item">
            <div id="sessionTime" class="stat-number">00:00</div>
            <div class="stat-label">会话时长</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载 LiveKit 客户端库 -->
    <script>
      // 改进的 LiveKit 客户端库加载函数
      function loadLiveKitClient() {
        return new Promise((resolve, reject) => {
          // 如果已经加载，直接返回
          if (typeof LivekitClient !== "undefined") {
            console.log("✅ LiveKit 客户端库已存在");
            resolve();
            return;
          }

          const cdnUrls = [
            "https://unpkg.com/livekit-client@2.5.0/dist/livekit-client.umd.js",
            "https://cdn.jsdelivr.net/npm/livekit-client@2.5.0/dist/livekit-client.umd.js",
            "https://unpkg.com/livekit-client@latest/dist/livekit-client.umd.js",
          ];

          let currentIndex = 0;

          function tryLoadScript() {
            if (currentIndex >= cdnUrls.length) {
              reject(new Error("所有 CDN 源都加载失败"));
              return;
            }

            const script = document.createElement("script");
            script.onload = () => {
              // 等待一小段时间确保库完全加载
              setTimeout(() => {
                if (typeof LivekitClient !== "undefined") {
                  console.log(
                    `✅ LiveKit 客户端库加载成功 (CDN ${currentIndex + 1})`
                  );
                  resolve();
                } else {
                  console.warn(
                    `⚠️ CDN ${currentIndex + 1} 加载后未找到 LivekitClient`
                  );
                  currentIndex++;
                  tryLoadScript();
                }
              }, 100);
            };

            script.onerror = () => {
              console.warn(
                `❌ CDN ${currentIndex + 1} 加载失败: ${cdnUrls[currentIndex]}`
              );
              currentIndex++;
              tryLoadScript();
            };

            script.src = cdnUrls[currentIndex];
            document.head.appendChild(script);
          }

          tryLoadScript();
        });
      }

      // 加载库
      loadLiveKitClient().catch((error) => {
        console.error("❌ LiveKit 客户端库加载失败:", error);
        // 显示友好的错误提示
        const status = document.querySelector(".status");
        if (status) {
          status.className = "status error";
          status.innerHTML =
            "⚠️ LiveKit 客户端库加载失败，请检查网络连接或刷新页面重试";
        }
      });
    </script>
    <!-- 转录脚本 -->
    <script src="advanced-deduplication.js"></script>
    <script src="transcription.js"></script>
    <script src="app.js"></script>
  </body>
</html>
